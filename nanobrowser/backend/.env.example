# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Pinecone Configuration
PINECONE_API_KEY=your_pinecone_api_key_here

# Gemini API Configuration (for embeddings)
GEMINI_API_KEY=your_gemini_api_key_here

# Refinement Configuration (Optional - defaults will be used if not set)
REFINEMENT_MIN_SPECIFICITY=0.6
REFINEMENT_SPECIFICITY_THRESHOLD=0.7
REFINEMENT_MIN_SIMILARITY=0.6
REFINEMENT_SEMANTIC_THRESHOLD=0.6
REFINEMENT_MIN_PROMPT_LENGTH=5
REFINEMENT_MAX_ITERATIONS=3
REFINEMENT_SESSION_TIMEOUT_HOURS=24
REFINEMENT_VECTOR_SEARCH_TOP_K=5
REFINEMENT_CONTEXT_CHUNKS=3
REFINEMENT_MAX_QUESTIONS=3
REFINEMENT_QUESTION_MODEL=gpt-4o-mini
REFINEMENT_QUESTION_TEMPERATURE=0.7
REFINEMENT_PROMPT_MODEL=gpt-4o-mini
REFINEMENT_PROMPT_TEMPERATURE=0.3
