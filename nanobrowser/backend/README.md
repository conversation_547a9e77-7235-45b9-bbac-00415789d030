# Nanobrowser Backend API

This is the backend API server for Nanobrowser that provides auto-refinement and vector database management functionality.

## Features

- **Auto Refinement**: Automatically refine user prompts using vector database context
- **Vector Database Management**: Full CRUD operations for Pinecone vector database
- **Document Upload**: Support for text and markdown file uploads
- **Search Functionality**: Semantic search across stored documents
- **Health Monitoring**: API health checks and database statistics

## Prerequisites

- Python 3.8+
- OpenAI API key
- Pinecone API key
- Google Gemini API key (for embeddings)

## Installation

1. **Clone the repository** (if not already done):
   ```bash
   git clone <repository-url>
   cd nanobrowser/backend
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` and add your API keys:
   ```env
   OPENAI_API_KEY=your_openai_api_key_here
   PINECONE_API_KEY=your_pinecone_api_key_here
   GEMINI_API_KEY=your_gemini_api_key_here
   ```

## Running the Server

### Development Mode
```bash
python start_server.py --reload
```

### Production Mode
```bash
python start_server.py --host 0.0.0.0 --port 8001
```

### Check Environment
```bash
python start_server.py --check-env
```

## API Endpoints

### Health Check
- `GET /api/v1/health` - Server health check
- `GET /api/v1/ping` - Simple ping endpoint

### Auto Refinement
- `POST /api/v1/auto-refine` - Automatically refine a prompt
- `GET /api/v1/auto-refine/health` - Auto refinement service health

### Vector Database Management
- `POST /api/v1/documents` - Add a single document
- `POST /api/v1/documents/batch` - Add multiple documents
- `POST /api/v1/documents/upload` - Upload and process a file
- `POST /api/v1/search` - Search for similar documents
- `DELETE /api/v1/documents/{document_id}` - Delete a document
- `GET /api/v1/stats` - Get database statistics
- `DELETE /api/v1/clear` - Clear all documents (danger zone)

## API Documentation

Once the server is running, you can access:
- **Swagger UI**: http://localhost:8001/docs
- **ReDoc**: http://localhost:8001/redoc

## Configuration

The following environment variables can be used to configure the refinement system:

```env
# Refinement thresholds
REFINEMENT_MIN_SPECIFICITY=0.6
REFINEMENT_SPECIFICITY_THRESHOLD=0.7
REFINEMENT_MIN_SIMILARITY=0.6
REFINEMENT_SEMANTIC_THRESHOLD=0.6

# Processing settings
REFINEMENT_MIN_PROMPT_LENGTH=5
REFINEMENT_MAX_ITERATIONS=3
REFINEMENT_VECTOR_SEARCH_TOP_K=5
REFINEMENT_CONTEXT_CHUNKS=3
REFINEMENT_MAX_QUESTIONS=3

# Model settings
REFINEMENT_QUESTION_MODEL=gpt-4o-mini
REFINEMENT_QUESTION_TEMPERATURE=0.7
REFINEMENT_PROMPT_MODEL=gpt-4o-mini
REFINEMENT_PROMPT_TEMPERATURE=0.3
```

## Usage Examples

### Auto Refine a Prompt
```bash
curl -X POST "http://localhost:8001/api/v1/auto-refine" \
  -H "Content-Type: application/json" \
  -d '{"prompt": "test the login page"}'
```

### Add a Document
```bash
curl -X POST "http://localhost:8001/api/v1/documents" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "To login to the application, click the Login button in the top right corner, enter your username and password, then click Submit.",
    "metadata": {"category": "authentication", "source": "manual"}
  }'
```

### Search Documents
```bash
curl -X POST "http://localhost:8001/api/v1/search" \
  -H "Content-Type: application/json" \
  -d '{"query": "how to login", "top_k": 5}'
```

## Frontend Integration

The Nanobrowser extension automatically integrates with this backend:

1. **Auto Refinement**: When enabled, prompts are automatically refined before execution
2. **Settings Management**: The Vector DB settings tab allows full database management
3. **Real-time Feedback**: Shows refined prompts with accept/reject options

## Troubleshooting

### Common Issues

1. **Connection Failed**: Ensure the backend server is running on the correct port
2. **API Key Errors**: Verify all required API keys are set in the `.env` file
3. **Pinecone Errors**: Check that your Pinecone index is properly configured
4. **Import Errors**: Ensure all dependencies are installed with `pip install -r requirements.txt`

### Logs

The server logs to console by default. For debugging, run with:
```bash
python start_server.py --reload
```

## Development

### Project Structure
```
backend/
├── controllers/          # API route handlers
├── services/            # Business logic
├── models/              # Data models
├── vector_db/           # Vector database client
├── prompt_refinement/   # Refinement logic
├── main.py             # FastAPI application
├── start_server.py     # Server startup script
└── requirements.txt    # Python dependencies
```

### Adding New Features

1. Add new endpoints in `controllers/`
2. Implement business logic in `services/`
3. Update the main app in `main.py` to include new routers
4. Test with the Swagger UI at `/docs`

## License

This project is part of the Nanobrowser extension.
