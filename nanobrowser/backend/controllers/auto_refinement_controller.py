"""
Auto-refinement controller for automatic prompt refinement using vector database.
"""

import logging
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from services.auto_refinement_service import AutoRefinementService

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(tags=["auto-refinement"])

# Global auto-refinement service instance
auto_refinement_service = AutoRefinementService()


class AutoRefinementRequest(BaseModel):
    """Request model for auto-refinement."""
    prompt: str = Field(..., description="The prompt to refine automatically")


class IterationLog(BaseModel):
    """Log entry for a single refinement iteration."""
    iteration: int
    questions: List[str]
    answers: List[str]
    specificity_score: float
    semantic_score: float
    thresholds_met: bool
    refined_prompt: Optional[str] = None


class AutoRefinementResponse(BaseModel):
    """Response model for auto-refinement."""
    success: bool
    message: str
    original_prompt: str
    final_prompt: str
    iterations_completed: int
    max_iterations: int
    final_specificity_score: float
    final_semantic_score: float
    thresholds_met: bool
    iteration_logs: List[IterationLog]
    final_analysis: Optional[Dict[str, Any]] = None


@router.post("/auto-refine", response_model=AutoRefinementResponse)
async def auto_refine_prompt(request: AutoRefinementRequest):
    """
    Automatically refine a prompt using vector database for context.
    
    This endpoint performs automatic refinement in a single iteration by:
    1. Analyzing the prompt for specificity and semantic alignment
    2. Generating clarifying questions if needed
    3. Automatically answering questions using vector database context (with anti-hallucination measures)
    4. Refining the prompt with the generated answers

    Single iteration prevents over-refinement and reduces hallucination risk.
    """
    try:
        logger.info(f"Starting auto-refinement for prompt: {request.prompt[:100]}...")
        
        # Perform auto-refinement (single iteration)
        result = auto_refinement_service.auto_refine_prompt(
            original_prompt=request.prompt
        )
        
        if not result["success"]:
            raise HTTPException(status_code=500, detail="Auto-refinement failed")
        
        # Convert iteration logs to response format
        iteration_logs = []
        for log in result.get("iteration_logs", []):
            iteration_logs.append(IterationLog(
                iteration=log["iteration"],
                questions=log["questions"],
                answers=log["answers"],
                specificity_score=log["specificity_score"],
                semantic_score=log["semantic_score"],
                thresholds_met=log["thresholds_met"],
                refined_prompt=log.get("refined_prompt")
            ))
        
        response = AutoRefinementResponse(
            success=True,
            message="Auto-refinement completed successfully",
            original_prompt=result["original_prompt"],
            final_prompt=result["final_prompt"],
            iterations_completed=result["iterations_completed"],
            max_iterations=result["max_iterations"],
            final_specificity_score=result["final_specificity_score"],
            final_semantic_score=result["final_semantic_score"],
            thresholds_met=result["thresholds_met"],
            iteration_logs=iteration_logs,
            final_analysis=result.get("final_analysis")
        )
        
        logger.info(f"Auto-refinement completed successfully - {result['iterations_completed']} iterations")
        return response
        
    except Exception as e:
        logger.error(f"Auto-refinement failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Auto-refinement failed: {str(e)}")


@router.get("/auto-refine/health")
async def auto_refinement_health():
    """Check the health of the auto-refinement service."""
    try:
        return {
            "status": "healthy",
            "vector_db_available": auto_refinement_service.vector_db_available,
            "service_ready": True
        }
    except Exception as e:
        logger.error(f"Auto-refinement health check failed: {e}")
        return {
            "status": "unhealthy",
            "vector_db_available": False,
            "error": str(e),
            "service_ready": False
        }
