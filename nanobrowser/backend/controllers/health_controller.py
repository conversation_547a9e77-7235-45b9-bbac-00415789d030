"""
Health check controller for Nanobrowser Backend API
"""

from fastapi import APIRouter
from pydantic import BaseModel
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

router = APIRouter(tags=["health"])

SERVER_START_TIME = datetime.now()


class HealthCheck(BaseModel):
    """Health check response model."""
    status: str
    timestamp: datetime
    version: str
    uptime: str


@router.get("/health", response_model=HealthCheck)
async def health_check():
    """Health check endpoint."""
    uptime = datetime.now() - SERVER_START_TIME
    uptime_str = str(uptime).split('.')[0]  # Remove microseconds
    
    return HealthCheck(
        status="healthy",
        timestamp=datetime.now(),
        version="1.0.0",
        uptime=uptime_str
    )


@router.get("/ping")
async def ping():
    """Simple ping endpoint."""
    return {"message": "pong", "timestamp": datetime.now()}
