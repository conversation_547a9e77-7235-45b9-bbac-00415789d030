"""
Vector Database Controller for managing Pinecone database entries
"""

import logging
from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from services.vector_db_service import VectorDBService

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(tags=["vector-db"])

# Global vector DB service instance
vector_db_service = VectorDBService()


class DocumentRequest(BaseModel):
    """Request model for adding a document."""
    text: str = Field(..., description="The document text content")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Optional metadata")


class DocumentResponse(BaseModel):
    """Response model for document operations."""
    success: bool
    message: str
    document_id: Optional[str] = None


class SearchRequest(BaseModel):
    """Request model for searching documents."""
    query: str = Field(..., description="Search query")
    top_k: int = Field(default=5, description="Number of results to return")
    filter_dict: Optional[Dict[str, Any]] = Field(default=None, description="Optional metadata filter")


class SearchResult(BaseModel):
    """Search result model."""
    id: str
    text: str
    score: float
    metadata: Dict[str, Any]


class SearchResponse(BaseModel):
    """Response model for search operations."""
    success: bool
    message: str
    results: List[SearchResult]


class DatabaseStatsResponse(BaseModel):
    """Response model for database statistics."""
    success: bool
    message: str
    total_vectors: int
    dimension: int
    index_fullness: float


@router.post("/documents", response_model=DocumentResponse)
async def add_document(request: DocumentRequest):
    """Add a single document to the vector database."""
    try:
        logger.info(f"Adding document: {request.text[:100]}...")
        
        result = await vector_db_service.add_document(
            text=request.text,
            metadata=request.metadata
        )
        
        return DocumentResponse(
            success=True,
            message="Document added successfully",
            document_id=result["document_id"]
        )
        
    except Exception as e:
        logger.error(f"Failed to add document: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to add document: {str(e)}")


@router.post("/documents/batch", response_model=DocumentResponse)
async def add_documents_batch(documents: List[DocumentRequest]):
    """Add multiple documents to the vector database."""
    try:
        logger.info(f"Adding {len(documents)} documents...")
        
        result = await vector_db_service.add_documents_batch(
            documents=[{"text": doc.text, "metadata": doc.metadata} for doc in documents]
        )
        
        return DocumentResponse(
            success=True,
            message=f"Successfully added {len(documents)} documents",
            document_id=None
        )
        
    except Exception as e:
        logger.error(f"Failed to add documents batch: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to add documents batch: {str(e)}")


@router.post("/documents/upload")
async def upload_document(
    file: UploadFile = File(...),
    metadata: Optional[str] = Form(default="{}")
):
    """Upload and process a document file."""
    try:
        import json
        metadata_dict = json.loads(metadata) if metadata else {}
        
        result = await vector_db_service.upload_and_process_file(file, metadata_dict)
        
        return DocumentResponse(
            success=True,
            message=f"File processed and {result['chunks_added']} chunks added",
            document_id=None
        )
        
    except Exception as e:
        logger.error(f"Failed to upload document: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to upload document: {str(e)}")


@router.post("/search", response_model=SearchResponse)
async def search_documents(request: SearchRequest):
    """Search for similar documents in the vector database."""
    try:
        logger.info(f"Searching for: {request.query[:100]}...")
        
        results = await vector_db_service.search(
            query=request.query,
            top_k=request.top_k,
            filter_dict=request.filter_dict
        )
        
        search_results = [
            SearchResult(
                id=result["id"],
                text=result["text"],
                score=result["score"],
                metadata=result["metadata"]
            )
            for result in results
        ]
        
        return SearchResponse(
            success=True,
            message=f"Found {len(search_results)} results",
            results=search_results
        )
        
    except Exception as e:
        logger.error(f"Search failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")


@router.delete("/documents/{document_id}")
async def delete_document(document_id: str):
    """Delete a document from the vector database."""
    try:
        logger.info(f"Deleting document: {document_id}")
        
        await vector_db_service.delete_document(document_id)
        
        return DocumentResponse(
            success=True,
            message="Document deleted successfully",
            document_id=document_id
        )
        
    except Exception as e:
        logger.error(f"Failed to delete document: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete document: {str(e)}")


@router.get("/stats", response_model=DatabaseStatsResponse)
async def get_database_stats():
    """Get database statistics."""
    try:
        stats = await vector_db_service.get_database_stats()
        
        return DatabaseStatsResponse(
            success=True,
            message="Database stats retrieved successfully",
            total_vectors=stats["total_vectors"],
            dimension=stats["dimension"],
            index_fullness=stats["index_fullness"]
        )
        
    except Exception as e:
        logger.error(f"Failed to get database stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get database stats: {str(e)}")


@router.delete("/clear")
async def clear_database():
    """Clear all documents from the vector database."""
    try:
        logger.warning("Clearing entire vector database...")
        
        await vector_db_service.clear_database()
        
        return DocumentResponse(
            success=True,
            message="Database cleared successfully",
            document_id=None
        )
        
    except Exception as e:
        logger.error(f"Failed to clear database: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to clear database: {str(e)}")
