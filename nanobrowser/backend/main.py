"""
Nanobrowser Backend API Server
Provides auto-refinement and vector database management functionality
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import logging
from contextlib import asynccontextmanager

from controllers.auto_refinement_controller import router as auto_refinement_router
from controllers.vector_db_controller import router as vector_db_router
from controllers.health_controller import router as health_router

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("🚀 Nanobrowser Backend API Server starting up...")
    yield
    # Shutdown
    logger.info("🛑 Nanobrowser Backend API Server shutting down...")


# Create FastAPI app with lifespan
app = FastAPI(
    title="Nanobrowser Backend API",
    description="Backend API for Nanobrowser auto-refinement and vector database management",
    version="1.0.0",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(health_router, prefix="/api/v1")
app.include_router(auto_refinement_router, prefix="/api/v1")
app.include_router(vector_db_router, prefix="/api/v1")


@app.get("/")
async def root():
    return {
        "message": "Welcome to Nanobrowser Backend API",
        "version": "1.0.0",
        "docs": "/docs",
        "redoc": "/redoc"
    }


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    logger.error(f"Global exception handler: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={"detail": f"Internal server error: {str(exc)}"}
    )


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8001,  # Different port from web-ui
        reload=True,
        log_level="info"
    )
