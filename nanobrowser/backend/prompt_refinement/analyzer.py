"""
Prompt Analysis and Refinement System

This module analyzes user prompts against the vector database to determine
if they need clarification and generates appropriate questions.
"""

import os
import logging
from typing import Dict, List, Any, Optional, Tuple
import openai
from dotenv import load_dotenv

from vector_db.pinecone_client import PineconeVectorDB
from prompt_refinement.config import RefinementConfig

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)


class PromptAnalyzer:
    """Analyzes prompts and determines if they need refinement."""
    
    def __init__(self):
        """Initialize the prompt analyzer."""
        self.vector_db = PineconeVectorDB()
        self.vector_db.create_index_if_not_exists()
        
        # Initialize OpenAI client
        self.openai_client = openai.OpenAI(
            api_key=os.getenv("OPENAI_API_KEY")
        )
        
        # Load configuration
        self.config = RefinementConfig()
        self.min_similarity_threshold = self.config.MIN_SIMILARITY_THRESHOLD
        self.vague_prompt_indicators = self.config.VAGUE_INDICATORS
        
    def analyze_prompt(self, user_prompt: str) -> Dict[str, Any]:
        """
        Analyze a user prompt to determine if it needs refinement.
        
        Args:
            user_prompt: The user's input prompt
            
        Returns:
            Dictionary containing analysis results
        """
        try:
            # Search vector database for relevant context
            search_results = self.vector_db.search(user_prompt, top_k=self.config.VECTOR_SEARCH_TOP_K)
            
            # Analyze prompt specificity
            specificity_score = self._calculate_specificity_score(user_prompt)

            # Calculate semantic score
            semantic_score = self._calculate_semantic_score(user_prompt, search_results)

            # Check if prompt has sufficient context
            has_sufficient_context = self._has_sufficient_context(user_prompt, search_results)
            
            # Determine if refinement is needed
            needs_refinement = (
                specificity_score < self.config.MIN_SPECIFICITY_THRESHOLD or
                not has_sufficient_context or
                len(user_prompt.split()) < self.config.MIN_PROMPT_LENGTH
            )
            
            return {
                'needs_refinement': needs_refinement,
                'specificity_score': specificity_score,
                'semantic_score': semantic_score,
                'has_sufficient_context': has_sufficient_context,
                'search_results': search_results,
                'word_count': len(user_prompt.split()),
                'analysis_details': {
                    'vague_indicators_found': self._find_vague_indicators(user_prompt),
                    'specific_elements_mentioned': self._find_specific_elements(user_prompt),
                    'action_clarity': self._assess_action_clarity(user_prompt)
                }
            }
            
        except Exception as e:
            logger.error(f"Error analyzing prompt: {e}")
            # Return safe defaults
            return {
                'needs_refinement': True,
                'specificity_score': 0.3,
                'semantic_score': 0.3,
                'has_sufficient_context': False,
                'search_results': [],
                'word_count': len(user_prompt.split()),
                'analysis_details': {
                    'error': str(e)
                }
            }
    
    def _calculate_specificity_score(self, prompt: str) -> float:
        """Calculate how specific the prompt is."""
        try:
            words = prompt.lower().split()
            word_count = len(words)
            
            # Base score from word count
            length_score = min(word_count / 20.0, 1.0)  # Max at 20 words
            
            # Check for specific elements
            specific_indicators = [
                'button', 'link', 'field', 'form', 'menu', 'dropdown',
                'checkbox', 'radio', 'input', 'textarea', 'select',
                'id=', 'class=', 'name=', 'xpath', 'css',
                'contains', 'exact', 'specific', 'particular'
            ]
            
            specificity_bonus = sum(1 for indicator in specific_indicators if indicator in prompt.lower()) * 0.1
            
            # Penalty for vague words
            vague_penalty = sum(1 for vague in self.vague_prompt_indicators if vague in words) * 0.05
            
            score = length_score + specificity_bonus - vague_penalty
            return max(0.0, min(1.0, score))
            
        except Exception as e:
            logger.error(f"Error calculating specificity score: {e}")
            return 0.3
    
    def _calculate_semantic_score(self, prompt: str, search_results: List[Dict]) -> float:
        """Calculate semantic alignment with knowledge base."""
        try:
            if not search_results:
                return 0.3
            
            # Average of top 3 similarity scores
            top_scores = [result['score'] for result in search_results[:3]]
            if not top_scores:
                return 0.3
            
            return sum(top_scores) / len(top_scores)
            
        except Exception as e:
            logger.error(f"Error calculating semantic score: {e}")
            return 0.3
    
    def _has_sufficient_context(self, prompt: str, search_results: List[Dict]) -> bool:
        """Check if prompt has sufficient context from knowledge base."""
        try:
            # Check if we have high-quality search results
            high_quality_results = [r for r in search_results if r['score'] > self.min_similarity_threshold]
            
            # Check prompt length and specificity
            word_count = len(prompt.split())
            has_specific_elements = any(indicator in prompt.lower() for indicator in [
                'button', 'link', 'field', 'form', 'input', 'click on', 'navigate to'
            ])
            
            return (
                len(high_quality_results) >= 2 and
                word_count >= self.config.MIN_PROMPT_LENGTH and
                has_specific_elements
            )
            
        except Exception as e:
            logger.error(f"Error checking context sufficiency: {e}")
            return False
    
    def _find_vague_indicators(self, prompt: str) -> List[str]:
        """Find vague indicators in the prompt."""
        words = prompt.lower().split()
        return [indicator for indicator in self.vague_prompt_indicators if indicator in words]
    
    def _find_specific_elements(self, prompt: str) -> List[str]:
        """Find specific UI elements mentioned in the prompt."""
        specific_elements = [
            'button', 'link', 'field', 'form', 'menu', 'dropdown',
            'checkbox', 'radio', 'input', 'textarea', 'select',
            'modal', 'dialog', 'popup', 'tab', 'panel'
        ]
        words = prompt.lower().split()
        return [element for element in specific_elements if element in words]
    
    def _assess_action_clarity(self, prompt: str) -> Dict[str, Any]:
        """Assess how clear the actions are in the prompt."""
        action_words = [
            'click', 'type', 'enter', 'select', 'choose', 'navigate',
            'scroll', 'drag', 'drop', 'hover', 'submit', 'save'
        ]
        words = prompt.lower().split()
        found_actions = [action for action in action_words if action in words]
        
        return {
            'actions_found': found_actions,
            'action_count': len(found_actions),
            'has_clear_actions': len(found_actions) > 0
        }

    def generate_questions(self, user_prompt: str, context_results: List[Dict[str, Any]]) -> List[str]:
        """Generate clarifying questions based on prompt analysis."""
        try:
            # Prepare context from search results
            context_texts = []
            for result in context_results[:self.config.CONTEXT_CHUNKS_FOR_QUESTIONS]:
                context_texts.append(f"[Score: {result['score']:.3f}] {result['text']}")

            context_text = "\n".join(context_texts) if context_texts else "No relevant context found."

            user_message = f"""Analyze this browser automation prompt and generate clarifying questions:

Prompt: "{user_prompt}"

Available context from knowledge base:
{context_text}

Generate 1-3 specific questions that would help make this prompt more actionable for browser automation."""

            response = self.openai_client.chat.completions.create(
                model=self.config.QUESTION_GENERATION_MODEL,
                messages=[
                    {"role": "system", "content": self.config.QUESTION_GENERATION_SYSTEM_PROMPT},
                    {"role": "user", "content": user_message}
                ],
                temperature=self.config.QUESTION_GENERATION_TEMPERATURE,
                max_tokens=300
            )

            questions_text = response.choices[0].message.content.strip()

            # Try to parse as JSON array
            import json
            try:
                questions = json.loads(questions_text)
                if isinstance(questions, list):
                    return questions[:self.config.MAX_QUESTIONS_PER_ITERATION]
            except json.JSONDecodeError:
                pass

            # Fallback: split by lines and clean up
            questions = [q.strip('- ').strip() for q in questions_text.split('\n') if q.strip()]
            questions = [q for q in questions if q and not q.startswith('[') and len(q) > 10]

            return questions[:self.config.MAX_QUESTIONS_PER_ITERATION] if questions else self.config.get_fallback_questions()

        except Exception as e:
            logger.error(f"Error generating questions: {e}")
            return self.config.get_fallback_questions()

    def refine_prompt(self, base_prompt: str, questions: List[str], answers: List[str]) -> str:
        """Refine the prompt using questions and answers."""
        try:
            # Combine questions and answers
            qa_pairs = []
            for q, a in zip(questions, answers):
                if a.strip():  # Only include non-empty answers
                    qa_pairs.append(f"Q: {q}\nA: {a}")

            qa_text = "\n\n".join(qa_pairs) if qa_pairs else "No additional clarification provided."

            user_message = f"""Original prompt: {base_prompt}

Clarifying information:
{qa_text}

Create a refined, detailed prompt that incorporates this additional information for browser automation."""

            response = self.openai_client.chat.completions.create(
                model=self.config.PROMPT_REFINEMENT_MODEL,
                messages=[
                    {"role": "system", "content": self.config.PROMPT_REFINEMENT_SYSTEM_PROMPT},
                    {"role": "user", "content": user_message}
                ],
                temperature=self.config.PROMPT_REFINEMENT_TEMPERATURE,
                max_tokens=500
            )

            refined_prompt = response.choices[0].message.content.strip()
            return refined_prompt

        except Exception as e:
            logger.error(f"Error refining prompt: {e}")
            # Fallback: combine base prompt with answers
            combined = f"{base_prompt}\n\nAdditional details:\n"
            for q, a in zip(questions, answers):
                if a.strip():
                    combined += f"- {q} {a}\n"
            return combined
