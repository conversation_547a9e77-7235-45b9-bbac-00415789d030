"""
Configuration for prompt refinement system
"""

import os
from typing import List


class RefinementConfig:
    """Configuration class for prompt refinement system."""
    
    # Scoring thresholds
    MIN_SPECIFICITY_THRESHOLD = float(os.getenv("REFINEMENT_MIN_SPECIFICITY", "0.6"))
    SPECIFICITY_THRESHOLD = float(os.getenv("REFINEMENT_SPECIFICITY_THRESHOLD", "0.7"))  # For auto-refinement completion
    MIN_SIMILARITY_THRESHOLD = float(os.getenv("REFINEMENT_MIN_SIMILARITY", "0.6"))
    SEMANTIC_SCORE_THRESHOLD = float(os.getenv("REFINEMENT_SEMANTIC_THRESHOLD", "0.6"))  # For auto-refinement completion
    MIN_PROMPT_LENGTH = int(os.getenv("REFINEMENT_MIN_PROMPT_LENGTH", "5"))
    
    # Session management
    MAX_ITERATIONS = int(os.getenv("REFINEMENT_MAX_ITERATIONS", "3"))
    SESSION_TIMEOUT_HOURS = int(os.getenv("REFINEMENT_SESSION_TIMEOUT_HOURS", "24"))
    
    # Vector search settings
    VECTOR_SEARCH_TOP_K = int(os.getenv("REFINEMENT_VECTOR_SEARCH_TOP_K", "5"))
    CONTEXT_CHUNKS_FOR_QUESTIONS = int(os.getenv("REFINEMENT_CONTEXT_CHUNKS", "3"))
    
    # Question generation
    MAX_QUESTIONS_PER_ITERATION = int(os.getenv("REFINEMENT_MAX_QUESTIONS", "3"))
    QUESTION_GENERATION_MODEL = os.getenv("REFINEMENT_QUESTION_MODEL", "gpt-4o-mini")
    QUESTION_GENERATION_TEMPERATURE = float(os.getenv("REFINEMENT_QUESTION_TEMPERATURE", "0.7"))
    
    # Prompt refinement
    PROMPT_REFINEMENT_MODEL = os.getenv("REFINEMENT_PROMPT_MODEL", "gpt-4o-mini")
    PROMPT_REFINEMENT_TEMPERATURE = float(os.getenv("REFINEMENT_PROMPT_TEMPERATURE", "0.3"))
    
    # Vague prompt indicators
    VAGUE_INDICATORS = [
        "test", "check", "verify", "make sure", "ensure", "validate",
        "click", "go to", "navigate", "open", "visit",
        "find", "search", "look for", "locate",
        "fill", "enter", "type", "input",
        "submit", "send", "save", "confirm",
        "login", "sign in", "authenticate",
        "logout", "sign out",
        "download", "upload", "attach",
        "delete", "remove", "clear",
        "edit", "modify", "update", "change",
        "select", "choose", "pick",
        "scroll", "swipe", "drag",
        "wait", "pause", "delay"
    ]
    
    # System prompts
    QUESTION_GENERATION_SYSTEM_PROMPT = """You are an expert at analyzing browser automation prompts and generating clarifying questions.

Your task is to analyze a user's prompt for browser automation and generate specific clarifying questions that would help make the prompt more actionable and precise.

Focus on:
1. Missing specific details about target elements (exact text, IDs, classes, positions)
2. Unclear navigation paths or page locations
3. Missing verification criteria or success conditions
4. Ambiguous actions or sequences
5. Missing context about the application or website

Generate 1-3 concise, specific questions that would significantly improve the prompt's clarity and actionability.

Return only a JSON array of questions, nothing else."""

    PROMPT_REFINEMENT_SYSTEM_PROMPT = """You are an expert at refining browser automation prompts to make them more specific and actionable.

Your task is to combine the original prompt with the clarifying information to create a detailed, actionable prompt for browser automation.

The refined prompt should:
1. Be specific about what pages/elements to interact with
2. Include exact steps to perform
3. Specify how to verify success
4. Be clear and unambiguous for automation

Return only the refined prompt, nothing else."""

    @classmethod
    def get_fallback_questions(cls) -> List[str]:
        """Get fallback questions when LLM generation fails."""
        return [
            "What specific page or section should I navigate to?",
            "What exact actions should I perform (click, type, select, etc.)?",
            "How should I verify that the task was completed successfully?"
        ]
    
    @classmethod
    def get_supported_file_extensions(cls) -> List[str]:
        """Get list of supported document file extensions."""
        return ['.docx', '.pdf', '.txt', '.md']
    
    @classmethod
    def get_document_chunk_settings(cls) -> dict:
        """Get document chunking settings."""
        return {
            'chunk_size': 1000,
            'chunk_overlap': 200,
            'separators': ['\n\n', '\n', '. ', ' ', '']
        }
