"""
Auto-refinement service that uses vector database to answer clarifying questions automatically.
"""

import logging
from typing import List, Dict, Any, Optional
from vector_db.pinecone_client import PineconeVectorDB
from prompt_refinement.analyzer import PromptAnalyzer
from prompt_refinement.config import RefinementConfig
import openai
import os

logger = logging.getLogger(__name__)


class AutoRefinementService:
    """Service for automatic prompt refinement using vector database."""
    
    def __init__(self):
        """Initialize the auto-refinement service."""
        self.analyzer = PromptAnalyzer()
        self.config = RefinementConfig()

        # Initialize OpenAI client for answer generation
        self.openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

        # Initialize vector DB (but don't fail if it's not available)
        self.vector_db_available = True
        try:
            self.vector_db = PineconeVectorDB()
            logger.info("Vector database initialized successfully")
        except Exception as e:
            logger.warning(f"Vector database not available: {e}")
            self.vector_db_available = False
            self.vector_db = None

    def search_knowledge_base(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """Search the knowledge base for relevant information."""
        if not self.vector_db_available or not self.vector_db:
            logger.warning("Vector database not available for search")
            return []

        try:
            return self.vector_db.search(query, top_k=top_k)
        except Exception as e:
            logger.error(f"Error searching knowledge base: {e}")
            return []
    
    def generate_answer_from_context(self, question: str, context_results: List[Dict[str, Any]]) -> str:
        """Generate an answer to a question using context from vector database."""
        try:
            # Check if we have sufficient context with good relevance scores
            relevant_results = [r for r in context_results if r.get('score', 0) > 0.7]

            if not relevant_results:
                logger.info(f"No relevant context found for question: {question[:50]}...")
                return self._get_conservative_fallback_answer(question)

            # Prepare context from high-relevance search results
            context_texts = []
            for result in relevant_results[:5]:  # Use top 5 relevant results
                context_texts.append(f"[Relevance: {result['score']:.3f}] {result['text']}")

            context_text = "\n".join(context_texts)

            system_prompt = """You are a helpful assistant that answers questions based ONLY on the provided context.

IMPORTANT RULES:
1. Only use information explicitly stated in the context
2. If the context doesn't contain enough information to answer the question, clearly state that you don't have sufficient information
3. Do not make assumptions or add information not present in the context
4. Be specific and concise in your answers
5. If multiple pieces of context are relevant, synthesize them appropriately"""

            user_message = f"""Question: {question}

Context:
{context_text}

Based ONLY on the above context, provide a specific answer. If the context doesn't contain enough information, clearly state that you don't have sufficient information."""

            response = self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_message}
                ],
                temperature=0.1,  # Lower temperature to reduce hallucination
                max_tokens=150
            )

            answer = response.choices[0].message.content.strip()

            # Check if the model indicated insufficient information
            if "don't have enough" in answer.lower() or "insufficient" in answer.lower():
                logger.info(f"Model indicated insufficient context for question: {question[:50]}...")
                return self._get_conservative_fallback_answer(question)

            logger.info(f"Generated answer from context: {answer[:50]}...")
            return answer

        except Exception as e:
            logger.error(f"Error generating answer from context: {e}")
            return self._get_conservative_fallback_answer(question)

    def _get_conservative_fallback_answer(self, question: str) -> str:
        """Get a conservative fallback answer when context is insufficient."""
        question_lower = question.lower()
        
        if "page" in question_lower or "navigate" in question_lower:
            return "Please specify the exact page URL or navigation path"
        elif "button" in question_lower or "click" in question_lower:
            return "Please provide the exact button text, ID, or location"
        elif "field" in question_lower or "input" in question_lower or "form" in question_lower:
            return "Please specify the exact field name, label, or identifier"
        elif "verify" in question_lower or "success" in question_lower:
            return "Please describe the specific elements or messages that indicate success"
        else:
            return "Please provide more specific details for this requirement"

    def auto_refine_prompt(self, original_prompt: str) -> Dict[str, Any]:
        """
        Automatically refine a prompt using vector database for context.
        Single iteration to prevent over-refinement and hallucination.

        Args:
            original_prompt: The original user prompt

        Returns:
            Dictionary with refinement results and iteration logs
        """
        logger.info(f"Starting auto-refinement for prompt: {original_prompt[:100]}...")

        # Initialize tracking - single iteration only
        current_prompt = original_prompt
        iteration_logs = []
        max_iterations = 1  # Fixed at 1 to prevent over-refinement

        try:
            # Analyze the prompt
            analysis = self.analyzer.analyze_prompt(original_prompt)

            # Check if refinement is needed
            if not analysis['needs_refinement']:
                logger.info("Prompt doesn't need refinement")
                return {
                    "success": True,
                    "original_prompt": original_prompt,
                    "final_prompt": original_prompt,
                    "iterations_completed": 0,
                    "max_iterations": max_iterations,
                    "final_specificity_score": analysis['specificity_score'],
                    "final_semantic_score": analysis['semantic_score'],
                    "thresholds_met": True,
                    "iteration_logs": [],
                    "final_analysis": analysis
                }

            # Single iteration refinement
            iteration = 1
            logger.info(f"Starting iteration {iteration}")

            # Generate clarifying questions
            questions = self.analyzer.generate_questions(current_prompt, analysis['search_results'])
            logger.info(f"Generated {len(questions)} questions")

            # Auto-answer questions using vector database
            answers = []
            question_contexts = []

            for question in questions:
                # Search knowledge base for relevant context with more chunks
                search_results = self.search_knowledge_base(question, top_k=10)
                question_contexts.append({
                    "question": question,
                    "search_results": search_results
                })

                # Generate answer from context
                answer = self.generate_answer_from_context(question, search_results)
                answers.append(answer)

                logger.info(f"Q: {question[:50]}... A: {answer[:50]}...")

            # Refine the prompt with the generated answers
            refined_prompt = self.analyzer.refine_prompt(current_prompt, questions, answers)

            # Re-analyze the refined prompt
            refined_analysis = self.analyzer.analyze_prompt(refined_prompt)

            # Check if thresholds are met
            thresholds_met = (
                refined_analysis['specificity_score'] >= self.config.SPECIFICITY_THRESHOLD and
                refined_analysis['semantic_score'] >= self.config.SEMANTIC_SCORE_THRESHOLD
            )

            # Log this iteration
            iteration_log = {
                "iteration": iteration,
                "questions": questions,
                "answers": answers,
                "specificity_score": refined_analysis['specificity_score'],
                "semantic_score": refined_analysis['semantic_score'],
                "thresholds_met": thresholds_met,
                "refined_prompt": refined_prompt
            }
            iteration_logs.append(iteration_log)

            logger.info(f"Iteration {iteration} completed - Specificity: {refined_analysis['specificity_score']:.3f}, Semantic: {refined_analysis['semantic_score']:.3f}")

            # Update current prompt
            current_prompt = refined_prompt

            return {
                "success": True,
                "original_prompt": original_prompt,
                "final_prompt": current_prompt,
                "iterations_completed": iteration,
                "max_iterations": max_iterations,
                "final_specificity_score": refined_analysis['specificity_score'],
                "final_semantic_score": refined_analysis['semantic_score'],
                "thresholds_met": thresholds_met,
                "iteration_logs": iteration_logs,
                "final_analysis": refined_analysis
            }

        except Exception as e:
            logger.error(f"Auto-refinement failed: {e}")
            return {
                "success": False,
                "original_prompt": original_prompt,
                "final_prompt": original_prompt,
                "iterations_completed": 0,
                "max_iterations": max_iterations,
                "final_specificity_score": 0.3,
                "final_semantic_score": 0.3,
                "thresholds_met": False,
                "iteration_logs": iteration_logs,
                "error": str(e)
            }
