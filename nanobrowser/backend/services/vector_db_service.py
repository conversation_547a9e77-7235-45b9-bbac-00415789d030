"""
Vector Database Service for managing Pinecone database operations
"""

import logging
from typing import List, Dict, Any, Optional
from fastapi import UploadFile
import aiofiles
import tempfile
import os
from vector_db.pinecone_client import PineconeVectorDB

logger = logging.getLogger(__name__)


class VectorDBService:
    """Service for managing vector database operations."""
    
    def __init__(self):
        """Initialize the vector database service."""
        try:
            self.vector_db = PineconeVectorDB()
            self.vector_db.create_index_if_not_exists()
            logger.info("Vector database service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize vector database service: {e}")
            raise
    
    async def add_document(self, text: str, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Add a single document to the vector database."""
        try:
            if metadata is None:
                metadata = {}
            
            documents = [{"text": text, "metadata": metadata}]
            self.vector_db.upsert_documents(documents)
            
            document_id = self.vector_db.generate_id(text)
            
            return {
                "success": True,
                "document_id": document_id,
                "message": "Document added successfully"
            }
            
        except Exception as e:
            logger.error(f"Failed to add document: {e}")
            raise
    
    async def add_documents_batch(self, documents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Add multiple documents to the vector database."""
        try:
            self.vector_db.upsert_documents(documents)
            
            return {
                "success": True,
                "documents_added": len(documents),
                "message": f"Successfully added {len(documents)} documents"
            }
            
        except Exception as e:
            logger.error(f"Failed to add documents batch: {e}")
            raise
    
    async def search(self, query: str, top_k: int = 5, filter_dict: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Search for similar documents in the vector database."""
        try:
            results = self.vector_db.search(query, top_k=top_k, filter_dict=filter_dict)
            return results
            
        except Exception as e:
            logger.error(f"Search failed: {e}")
            raise
    
    async def delete_document(self, document_id: str) -> Dict[str, Any]:
        """Delete a document from the vector database."""
        try:
            self.vector_db.delete_document(document_id)
            
            return {
                "success": True,
                "message": f"Document {document_id} deleted successfully"
            }
            
        except Exception as e:
            logger.error(f"Failed to delete document: {e}")
            raise
    
    async def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics."""
        try:
            stats = self.vector_db.get_stats()
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get database stats: {e}")
            raise
    
    async def clear_database(self) -> Dict[str, Any]:
        """Clear all documents from the vector database."""
        try:
            self.vector_db.clear_all()
            
            return {
                "success": True,
                "message": "Database cleared successfully"
            }
            
        except Exception as e:
            logger.error(f"Failed to clear database: {e}")
            raise
    
    async def upload_and_process_file(self, file: UploadFile, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Upload and process a document file."""
        try:
            if metadata is None:
                metadata = {}
            
            # Add file metadata
            metadata.update({
                "filename": file.filename,
                "content_type": file.content_type,
                "file_size": file.size if hasattr(file, 'size') else None
            })
            
            # Read file content
            content = await file.read()
            
            # Process based on file type
            if file.content_type == "text/plain" or file.filename.endswith('.txt'):
                text_content = content.decode('utf-8')
                chunks = self._chunk_text(text_content)
            elif file.filename.endswith('.md'):
                text_content = content.decode('utf-8')
                chunks = self._chunk_text(text_content)
            else:
                # For other file types, treat as plain text for now
                try:
                    text_content = content.decode('utf-8')
                    chunks = self._chunk_text(text_content)
                except UnicodeDecodeError:
                    raise ValueError(f"Unsupported file type: {file.content_type}")
            
            # Create documents for each chunk
            documents = []
            for i, chunk in enumerate(chunks):
                chunk_metadata = metadata.copy()
                chunk_metadata.update({
                    "chunk_index": i,
                    "total_chunks": len(chunks)
                })
                documents.append({
                    "text": chunk,
                    "metadata": chunk_metadata
                })
            
            # Add documents to vector database
            self.vector_db.upsert_documents(documents)
            
            return {
                "success": True,
                "chunks_added": len(documents),
                "message": f"File processed and {len(documents)} chunks added"
            }
            
        except Exception as e:
            logger.error(f"Failed to upload and process file: {e}")
            raise
    
    def _chunk_text(self, text: str, chunk_size: int = 1000, chunk_overlap: int = 200) -> List[str]:
        """Split text into chunks for processing."""
        if len(text) <= chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            
            # If this isn't the last chunk, try to break at a sentence or word boundary
            if end < len(text):
                # Look for sentence boundary
                sentence_end = text.rfind('.', start, end)
                if sentence_end > start + chunk_size // 2:
                    end = sentence_end + 1
                else:
                    # Look for word boundary
                    word_end = text.rfind(' ', start, end)
                    if word_end > start + chunk_size // 2:
                        end = word_end
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            # Move start position with overlap
            start = end - chunk_overlap
            if start >= len(text):
                break
        
        return chunks
