#!/usr/bin/env python3
"""
Nanobrowser Backend Server Startup Script
"""

import argparse
import sys
import os
import logging
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_environment():
    """Check if required environment variables are set."""
    required_vars = [
        "OPENAI_API_KEY",
        "PINECONE_API_KEY", 
        "GEMINI_API_KEY"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error("Missing required environment variables:")
        for var in missing_vars:
            logger.error(f"  - {var}")
        logger.error("Please set these variables in your .env file or environment")
        logger.error("See .env.example for reference")
        return False
    
    return True


def run_server(host: str = "0.0.0.0", port: int = 8001, reload: bool = False):
    """Run the FastAPI server."""
    try:
        import uvicorn
        from main import app
        
        logger.info(f"Starting Nanobrowser Backend API server on {host}:{port}")
        logger.info(f"API Documentation: http://{host}:{port}/docs")
        logger.info(f"Reload mode: {reload}")
        
        uvicorn.run(
            "main:app",
            host=host,
            port=port,
            reload=reload,
            log_level="info"
        )
        
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        sys.exit(1)


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Nanobrowser Backend API Server")
    parser.add_argument("--host", type=str, default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8001, help="Port to listen on")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload for development")
    parser.add_argument("--check-env", action="store_true", help="Check environment variables and exit")
    
    args = parser.parse_args()
    
    # Load environment variables from .env file if it exists
    env_file = backend_dir / ".env"
    if env_file.exists():
        from dotenv import load_dotenv
        load_dotenv(env_file)
        logger.info(f"Loaded environment variables from {env_file}")
    else:
        logger.warning(f"No .env file found at {env_file}")
        logger.warning("Make sure to set required environment variables")
    
    # Check environment variables
    if not check_environment():
        sys.exit(1)
    
    if args.check_env:
        logger.info("Environment check passed!")
        sys.exit(0)
    
    # Start the server
    run_server(host=args.host, port=args.port, reload=args.reload)


if __name__ == "__main__":
    main()
