#!/usr/bin/env python3
"""
Simple test script for the Nanobrowser Backend API
"""

import requests
import json
import sys
import time

BASE_URL = "http://localhost:8001"

def test_health():
    """Test health endpoint"""
    print("Testing health endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/api/v1/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check passed: {data['status']}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_auto_refinement():
    """Test auto refinement endpoint"""
    print("\nTesting auto refinement...")
    try:
        payload = {
            "prompt": "test the login page"
        }
        response = requests.post(
            f"{BASE_URL}/api/v1/auto-refine",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Auto refinement successful")
            print(f"   Original: {data['original_prompt']}")
            print(f"   Refined:  {data['final_prompt']}")
            print(f"   Iterations: {data['iterations_completed']}")
            return True
        else:
            print(f"❌ Auto refinement failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Auto refinement error: {e}")
        return False

def test_document_management():
    """Test document CRUD operations"""
    print("\nTesting document management...")
    
    # Add a document
    try:
        payload = {
            "text": "To login to the application, navigate to the login page, enter your username and password in the respective fields, and click the Submit button.",
            "metadata": {"category": "authentication", "source": "test"}
        }
        response = requests.post(
            f"{BASE_URL}/api/v1/documents",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Document added successfully")
            print(f"   Document ID: {data.get('document_id', 'N/A')}")
            document_id = data.get('document_id')
        else:
            print(f"❌ Document addition failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Document addition error: {e}")
        return False
    
    # Search for documents
    try:
        time.sleep(2)  # Wait for indexing
        payload = {
            "query": "how to login",
            "top_k": 3
        }
        response = requests.post(
            f"{BASE_URL}/api/v1/search",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Document search successful")
            print(f"   Found {len(data['results'])} results")
            for i, result in enumerate(data['results'][:2]):
                print(f"   Result {i+1}: Score {result['score']:.3f}")
                print(f"   Text: {result['text'][:100]}...")
            return True
        else:
            print(f"❌ Document search failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Document search error: {e}")
        return False

def test_database_stats():
    """Test database statistics"""
    print("\nTesting database statistics...")
    try:
        response = requests.get(f"{BASE_URL}/api/v1/stats")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Database stats retrieved")
            print(f"   Total vectors: {data['total_vectors']}")
            print(f"   Dimensions: {data['dimension']}")
            print(f"   Index fullness: {data['index_fullness']:.1%}")
            return True
        else:
            print(f"❌ Database stats failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Database stats error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Nanobrowser Backend API Test Suite")
    print("=" * 50)
    
    tests = [
        ("Health Check", test_health),
        ("Auto Refinement", test_auto_refinement),
        ("Document Management", test_document_management),
        ("Database Statistics", test_database_stats),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        if test_func():
            passed += 1
        else:
            print(f"⚠️  {test_name} failed - check server logs")
    
    print("\n" + "=" * 50)
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The API is working correctly.")
        sys.exit(0)
    else:
        print("❌ Some tests failed. Please check the server configuration.")
        sys.exit(1)

if __name__ == "__main__":
    main()
