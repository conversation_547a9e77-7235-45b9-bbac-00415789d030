"""
Pinecone Vector Database Client with Gemini Embeddings
"""

import os
import logging
from typing import List, Dict, Any, Optional
import google.generativeai as genai
from pinecone import Pinecone, ServerlessSpec
import time
import hashlib

logger = logging.getLogger(__name__)


class PineconeVectorDB:
    """Pinecone vector database client with Gemini embeddings."""
    
    def __init__(self, index_name: str = "nanobrowser-refinement"):
        """Initialize the Pinecone client."""
        self.index_name = index_name
        self.index = None
        self.pc = None
        
        # Initialize Pinecone
        api_key = os.getenv("PINECONE_API_KEY")
        if not api_key:
            raise ValueError("PINECONE_API_KEY environment variable is required")
        
        self.pc = Pinecone(api_key=api_key)
        
        # Initialize Gemini
        gemini_api_key = os.getenv("GEMINI_API_KEY")
        if not gemini_api_key:
            raise ValueError("GEMINI_API_KEY environment variable is required")
        
        genai.configure(api_key=gemini_api_key)
        
        logger.info("Pinecone and Gemini clients initialized")
    
    def create_index_if_not_exists(self):
        """Create index if it doesn't exist."""
        try:
            # Check if index exists
            existing_indexes = [index.name for index in self.pc.list_indexes()]
            
            if self.index_name not in existing_indexes:
                logger.info(f"Creating index: {self.index_name}")
                
                # Create index with serverless spec
                self.pc.create_index(
                    name=self.index_name,
                    dimension=768,  # Gemini embedding dimension
                    metric="cosine",
                    spec=ServerlessSpec(
                        cloud="aws",
                        region="us-east-1"
                    )
                )
                
                # Wait for index to be ready
                while not self.pc.describe_index(self.index_name).status['ready']:
                    logger.info("Waiting for index to be ready...")
                    time.sleep(1)
                
                logger.info(f"Index {self.index_name} created successfully")
            else:
                logger.info(f"Index {self.index_name} already exists")
            
            # Connect to index
            self.index = self.pc.Index(self.index_name)
            logger.info(f"Connected to index: {self.index_name}")
            
        except Exception as e:
            logger.error(f"Error creating/connecting to index: {e}")
            raise
    
    def get_embedding(self, text: str) -> List[float]:
        """Get embedding for text using Gemini."""
        try:
            result = genai.embed_content(
                model="models/embedding-001",
                content=text,
                task_type="retrieval_document"
            )
            return result['embedding']
        except Exception as e:
            logger.error(f"Error getting embedding: {e}")
            raise
    
    def generate_id(self, text: str) -> str:
        """Generate a unique ID for text content."""
        return hashlib.md5(text.encode()).hexdigest()
    
    def upsert_documents(self, documents: List[Dict[str, Any]]):
        """
        Upsert documents to the vector database.
        
        Args:
            documents: List of documents with 'text' and 'metadata' keys
        """
        if not self.index:
            self.create_index_if_not_exists()
        
        vectors = []
        for i, doc in enumerate(documents):
            text = doc['text']
            metadata = doc.get('metadata', {})

            logger.info(f"Processing document {i+1}/{len(documents)}: {text[:100]}...")

            # Generate embedding
            embedding = self.get_embedding(text)
            logger.info(f"Generated embedding with dimension: {len(embedding)}")

            # Generate unique ID
            doc_id = self.generate_id(text)
            logger.info(f"Generated ID: {doc_id}")

            vectors.append({
                'id': doc_id,
                'values': embedding,
                'metadata': {
                    'text': text,
                    **metadata
                }
            })

        # Upsert vectors in batches
        batch_size = 100
        for i in range(0, len(vectors), batch_size):
            batch = vectors[i:i + batch_size]
            logger.info(f"Upserting batch {i//batch_size + 1}: {len(batch)} vectors")
            self.index.upsert(vectors=batch)
        
        logger.info(f"Successfully upserted {len(vectors)} documents")
    
    def search(self, query: str, top_k: int = 5, filter_dict: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """
        Search for similar documents.
        
        Args:
            query: Search query
            top_k: Number of results to return
            filter_dict: Optional metadata filter
            
        Returns:
            List of matching documents with scores
        """
        if not self.index:
            self.create_index_if_not_exists()
        
        # Get query embedding
        query_embedding = self.get_embedding(query)
        
        # Search
        results = self.index.query(
            vector=query_embedding,
            top_k=top_k,
            include_metadata=True,
            filter=filter_dict
        )
        
        # Format results
        formatted_results = []
        for match in results['matches']:
            formatted_results.append({
                'id': match['id'],
                'score': match['score'],
                'text': match['metadata'].get('text', ''),
                'metadata': {k: v for k, v in match['metadata'].items() if k != 'text'}
            })
        
        logger.info(f"Search returned {len(formatted_results)} results")
        return formatted_results
    
    def delete_document(self, document_id: str):
        """Delete a document by ID."""
        if not self.index:
            self.create_index_if_not_exists()
        
        self.index.delete(ids=[document_id])
        logger.info(f"Deleted document: {document_id}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get index statistics."""
        if not self.index:
            self.create_index_if_not_exists()
        
        stats = self.index.describe_index_stats()
        return {
            'total_vectors': stats['total_vector_count'],
            'dimension': stats['dimension'],
            'index_fullness': stats['index_fullness']
        }
    
    def clear_all(self):
        """Clear all vectors from the index."""
        if not self.index:
            self.create_index_if_not_exists()
        
        # Delete all vectors
        self.index.delete(delete_all=True)
        logger.info("Cleared all vectors from index")
