#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/jsonrepair@3.12.0/node_modules/jsonrepair/bin/node_modules:/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/jsonrepair@3.12.0/node_modules/jsonrepair/node_modules:/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/jsonrepair@3.12.0/node_modules:/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/jsonrepair@3.12.0/node_modules/jsonrepair/bin/node_modules:/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/jsonrepair@3.12.0/node_modules/jsonrepair/node_modules:/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/jsonrepair@3.12.0/node_modules:/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../jsonrepair/bin/cli.js" "$@"
else
  exec node  "$basedir/../jsonrepair/bin/cli.js" "$@"
fi
