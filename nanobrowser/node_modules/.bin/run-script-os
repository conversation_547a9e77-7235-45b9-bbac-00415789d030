#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/run-script-os@1.1.6/node_modules/run-script-os/node_modules:/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/run-script-os@1.1.6/node_modules:/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/run-script-os@1.1.6/node_modules/run-script-os/node_modules:/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/run-script-os@1.1.6/node_modules:/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../run-script-os/index.js" "$@"
else
  exec node  "$basedir/../run-script-os/index.js" "$@"
fi
