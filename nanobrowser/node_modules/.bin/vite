#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/vite@6.3.5_@types+node@22.15.23_jiti@1.21.7_terser@5.40.0_tsx@4.19.4_yaml@2.8.0/node_modules/vite/bin/node_modules:/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/vite@6.3.5_@types+node@22.15.23_jiti@1.21.7_terser@5.40.0_tsx@4.19.4_yaml@2.8.0/node_modules/vite/node_modules:/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/vite@6.3.5_@types+node@22.15.23_jiti@1.21.7_terser@5.40.0_tsx@4.19.4_yaml@2.8.0/node_modules:/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/vite@6.3.5_@types+node@22.15.23_jiti@1.21.7_terser@5.40.0_tsx@4.19.4_yaml@2.8.0/node_modules/vite/bin/node_modules:/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/vite@6.3.5_@types+node@22.15.23_jiti@1.21.7_terser@5.40.0_tsx@4.19.4_yaml@2.8.0/node_modules/vite/node_modules:/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/vite@6.3.5_@types+node@22.15.23_jiti@1.21.7_terser@5.40.0_tsx@4.19.4_yaml@2.8.0/node_modules:/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../vite/bin/vite.js" "$@"
fi
