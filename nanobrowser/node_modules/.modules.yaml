hoistPattern:
  - '*'
hoistedDependencies:
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@anthropic-ai/sdk@0.39.0':
    '@anthropic-ai/sdk': private
  '@cerebras/cerebras_cloud_sdk@1.35.0':
    '@cerebras/cerebras_cloud_sdk': private
  '@cfworker/json-schema@4.1.1':
    '@cfworker/json-schema': private
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@esbuild/aix-ppc64@0.25.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.5':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.5':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.5':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.5':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.5':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.5':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.5':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.5':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.5':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.5':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.5':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.5':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.5':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.5':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.5':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.5':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.5':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.5':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.5':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.5':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.5':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.5':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.5':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.5':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@8.57.0)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/eslintrc@2.1.4':
    '@eslint/eslintrc': private
  '@eslint/js@8.57.0':
    '@eslint/js': private
  '@google/generative-ai@0.24.1':
    '@google/generative-ai': private
  '@humanwhocodes/config-array@0.11.14':
    '@humanwhocodes/config-array': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/object-schema@2.0.3':
    '@humanwhocodes/object-schema': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/source-map@0.3.6':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.9':
    '@jridgewell/trace-mapping': private
  '@langchain/anthropic@0.3.21(@langchain/core@0.3.57(openai@4.103.0(ws@8.18.2)(zod@3.25.58)))':
    '@langchain/anthropic': private
  '@langchain/cerebras@0.0.1(@langchain/core@0.3.57(openai@4.103.0(ws@8.18.2)(zod@3.25.58)))':
    '@langchain/cerebras': private
  '@langchain/core@0.3.57(openai@4.103.0(ws@8.18.2)(zod@3.25.58))':
    '@langchain/core': private
  '@langchain/deepseek@0.0.2(@langchain/core@0.3.57(openai@4.103.0(ws@8.18.2)(zod@3.25.58)))(ws@8.18.2)':
    '@langchain/deepseek': private
  '@langchain/google-genai@0.2.10(@langchain/core@0.3.57(openai@4.103.0(ws@8.18.2)(zod@3.25.58)))(zod@3.25.58)':
    '@langchain/google-genai': private
  '@langchain/groq@0.2.2(@langchain/core@0.3.57(openai@4.103.0(ws@8.18.2)(zod@3.25.58)))':
    '@langchain/groq': private
  '@langchain/ollama@0.2.1(@langchain/core@0.3.57(openai@4.103.0(ws@8.18.2)(zod@3.25.58)))':
    '@langchain/ollama': private
  '@langchain/openai@0.5.12(@langchain/core@0.3.57(openai@4.103.0(ws@8.18.2)(zod@3.25.58)))(ws@8.18.2)':
    '@langchain/openai': private
  '@langchain/xai@0.0.3(@langchain/core@0.3.57(openai@4.103.0(ws@8.18.2)(zod@3.25.58)))(ws@8.18.2)':
    '@langchain/xai': private
  '@laynezh/vite-plugin-lib-assets@0.6.1(vite@6.3.5(@types/node@22.15.23)(jiti@1.21.7)(terser@5.40.0)(tsx@4.19.4)(yaml@2.8.0))':
    '@laynezh/vite-plugin-lib-assets': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@pkgr/core@0.2.4':
    '@pkgr/core': private
  '@puppeteer/browsers@2.10.5':
    '@puppeteer/browsers': private
  '@rolldown/pluginutils@1.0.0-beta.9':
    '@rolldown/pluginutils': private
  '@rollup/plugin-sucrase@5.0.2(rollup@4.41.1)':
    '@rollup/plugin-sucrase': private
  '@rollup/pluginutils@5.1.4(rollup@4.41.1)':
    '@rollup/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.41.1':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.41.1':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.41.1':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.41.1':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.41.1':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.41.1':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.41.1':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.41.1':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.41.1':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.41.1':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.41.1':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.41.1':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.41.1':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.41.1':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.41.1':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.41.1':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.41.1':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.41.1':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.41.1':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.41.1':
    '@rollup/rollup-win32-x64-msvc': private
  '@swc/core-darwin-arm64@1.11.29':
    '@swc/core-darwin-arm64': private
  '@swc/core-darwin-x64@1.11.29':
    '@swc/core-darwin-x64': private
  '@swc/core-linux-arm-gnueabihf@1.11.29':
    '@swc/core-linux-arm-gnueabihf': private
  '@swc/core-linux-arm64-gnu@1.11.29':
    '@swc/core-linux-arm64-gnu': private
  '@swc/core-linux-arm64-musl@1.11.29':
    '@swc/core-linux-arm64-musl': private
  '@swc/core-linux-x64-gnu@1.11.29':
    '@swc/core-linux-x64-gnu': private
  '@swc/core-linux-x64-musl@1.11.29':
    '@swc/core-linux-x64-musl': private
  '@swc/core-win32-arm64-msvc@1.11.29':
    '@swc/core-win32-arm64-msvc': private
  '@swc/core-win32-ia32-msvc@1.11.29':
    '@swc/core-win32-ia32-msvc': private
  '@swc/core-win32-x64-msvc@1.11.29':
    '@swc/core-win32-x64-msvc': private
  '@swc/core@1.11.29':
    '@swc/core': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/types@0.1.21':
    '@swc/types': private
  '@tootallnate/quickjs-emscripten@0.23.0':
    '@tootallnate/quickjs-emscripten': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  '@types/eslint-scope@3.7.7':
    '@types/eslint-scope': private
  '@types/eslint@9.6.1':
    '@types/eslint': private
  '@types/estree@1.0.7':
    '@types/estree': private
  '@types/filesystem@0.0.36':
    '@types/filesystem': private
  '@types/filewriter@0.0.33':
    '@types/filewriter': private
  '@types/har-format@1.2.16':
    '@types/har-format': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/json5@0.0.29':
    '@types/json5': private
  '@types/node-fetch@2.6.12':
    '@types/node-fetch': private
  '@types/prop-types@15.7.14':
    '@types/prop-types': private
  '@types/retry@0.12.0':
    '@types/retry': private
  '@types/uuid@10.0.0':
    '@types/uuid': private
  '@types/ws@8.18.1':
    '@types/ws': private
  '@types/yauzl@2.10.3':
    '@types/yauzl': private
  '@typescript-eslint/scope-manager@7.18.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/type-utils@7.18.0(eslint@8.57.0)(typescript@5.5.4)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@7.18.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@7.18.0(typescript@5.5.4)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@7.18.0(eslint@8.57.0)(typescript@5.5.4)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@7.18.0':
    '@typescript-eslint/visitor-keys': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@vitejs/plugin-react-swc@3.10.0(vite@6.3.5(@types/node@22.15.23)(jiti@1.21.7)(terser@5.40.0)(tsx@4.19.4)(yaml@2.8.0))':
    '@vitejs/plugin-react-swc': private
  '@webassemblyjs/ast@1.14.1':
    '@webassemblyjs/ast': private
  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    '@webassemblyjs/floating-point-hex-parser': private
  '@webassemblyjs/helper-api-error@1.13.2':
    '@webassemblyjs/helper-api-error': private
  '@webassemblyjs/helper-buffer@1.14.1':
    '@webassemblyjs/helper-buffer': private
  '@webassemblyjs/helper-numbers@1.13.2':
    '@webassemblyjs/helper-numbers': private
  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    '@webassemblyjs/helper-wasm-bytecode': private
  '@webassemblyjs/helper-wasm-section@1.14.1':
    '@webassemblyjs/helper-wasm-section': private
  '@webassemblyjs/ieee754@1.13.2':
    '@webassemblyjs/ieee754': private
  '@webassemblyjs/leb128@1.13.2':
    '@webassemblyjs/leb128': private
  '@webassemblyjs/utf8@1.13.2':
    '@webassemblyjs/utf8': private
  '@webassemblyjs/wasm-edit@1.14.1':
    '@webassemblyjs/wasm-edit': private
  '@webassemblyjs/wasm-gen@1.14.1':
    '@webassemblyjs/wasm-gen': private
  '@webassemblyjs/wasm-opt@1.14.1':
    '@webassemblyjs/wasm-opt': private
  '@webassemblyjs/wasm-parser@1.14.1':
    '@webassemblyjs/wasm-parser': private
  '@webassemblyjs/wast-printer@1.14.1':
    '@webassemblyjs/wast-printer': private
  '@xtuc/ieee754@1.2.0':
    '@xtuc/ieee754': private
  '@xtuc/long@4.2.2':
    '@xtuc/long': private
  abort-controller@3.0.0:
    abort-controller: private
  acorn-jsx@5.3.2(acorn@8.14.1):
    acorn-jsx: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.14.1:
    acorn: private
  agent-base@7.1.3:
    agent-base: private
  agentkeepalive@4.6.0:
    agentkeepalive: private
  ajv-formats@2.1.1(ajv@8.17.1):
    ajv-formats: private
  ajv-keywords@5.1.0(ajv@8.17.1):
    ajv-keywords: private
  ajv@6.12.6:
    ajv: private
  ansi-escapes@7.0.0:
    ansi-escapes: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@5.2.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  arg@5.0.2:
    arg: private
  argparse@2.0.1:
    argparse: private
  aria-query@5.1.3:
    aria-query: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-includes@3.1.8:
    array-includes: private
  array-union@2.1.0:
    array-union: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.findlastindex@1.2.6:
    array.prototype.findlastindex: private
  array.prototype.flat@1.3.3:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  ast-types-flow@0.0.8:
    ast-types-flow: private
  ast-types@0.13.4:
    ast-types: private
  async-function@1.0.0:
    async-function: private
  asynckit@0.4.0:
    asynckit: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axe-core@4.10.3:
    axe-core: private
  axobject-query@3.1.1:
    axobject-query: private
  b4a@1.6.7:
    b4a: private
  balanced-match@1.0.2:
    balanced-match: private
  bare-events@2.5.4:
    bare-events: private
  bare-fs@4.1.5:
    bare-fs: private
  bare-os@3.6.1:
    bare-os: private
  bare-path@3.0.0:
    bare-path: private
  bare-stream@2.6.5(bare-events@2.5.4):
    bare-stream: private
  base64-js@1.5.1:
    base64-js: private
  basic-ftp@5.0.5:
    basic-ftp: private
  binary-extensions@2.3.0:
    binary-extensions: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.24.5:
    browserslist: private
  buffer-crc32@0.2.13:
    buffer-crc32: private
  buffer-from@1.1.2:
    buffer-from: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camelcase-css@2.0.1:
    camelcase-css: private
  camelcase@6.3.0:
    camelcase: private
  caniuse-lite@1.0.30001718:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  chokidar@3.6.0:
    chokidar: private
  chrome-extension:
    chrome-extension: private
  chrome-trace-event@1.0.4:
    chrome-trace-event: private
  chromium-bidi@5.1.0(devtools-protocol@0.0.1452169):
    chromium-bidi: private
  cli-cursor@5.0.0:
    cli-cursor: private
  cli-truncate@4.0.0:
    cli-truncate: private
  cliui@8.0.1:
    cliui: private
  clsx@2.1.1:
    clsx: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  colorette@2.0.20:
    colorette: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@13.1.0:
    commander: private
  concat-map@0.0.1:
    concat-map: private
  confusing-browser-globals@1.0.11:
    confusing-browser-globals: private
  console-table-printer@2.14.0:
    console-table-printer: private
  create-require@1.1.1:
    create-require: private
  cross-spawn@7.0.6:
    cross-spawn: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  damerau-levenshtein@1.0.8:
    damerau-levenshtein: private
  data-uri-to-buffer@6.0.2:
    data-uri-to-buffer: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  debug@4.4.1:
    debug: private
  decamelize@1.2.0:
    decamelize: private
  deep-equal@2.2.3:
    deep-equal: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@4.3.1:
    deepmerge: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  degenerator@5.0.1:
    degenerator: private
  delayed-stream@1.0.0:
    delayed-stream: private
  devtools-protocol@0.0.1452169:
    devtools-protocol: private
  didyoumean@1.2.2:
    didyoumean: private
  diff@4.0.2:
    diff: private
  dir-glob@3.0.1:
    dir-glob: private
  dlv@1.1.3:
    dlv: private
  doctrine@2.1.0:
    doctrine: private
  dunder-proto@1.0.1:
    dunder-proto: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  electron-to-chromium@1.5.159:
    electron-to-chromium: private
  emoji-regex@9.2.2:
    emoji-regex: private
  end-of-stream@1.4.4:
    end-of-stream: private
  enhanced-resolve@5.18.1:
    enhanced-resolve: private
  environment@1.1.0:
    environment: private
  es-abstract@1.23.10:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-get-iterator@1.1.3:
    es-get-iterator: private
  es-iterator-helpers@1.2.1:
    es-iterator-helpers: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-shim-unscopables@1.1.0:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  escodegen@2.1.0:
    escodegen: private
  eslint-config-airbnb-base@15.0.0(eslint-plugin-import@2.29.1(@typescript-eslint/parser@7.18.0(eslint@8.57.0)(typescript@5.5.4))(eslint@8.57.0))(eslint@8.57.0):
    eslint-config-airbnb-base: private
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: private
  eslint-module-utils@2.12.0(@typescript-eslint/parser@7.18.0(eslint@8.57.0)(typescript@5.5.4))(eslint-import-resolver-node@0.3.9)(eslint@8.57.0):
    eslint-module-utils: private
  eslint-scope@7.2.2:
    eslint-scope: private
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: private
  esm@3.2.25:
    esm: private
  espree@9.6.1:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@2.0.2:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  event-target-shim@5.0.1:
    event-target-shim: private
  eventemitter3@5.0.1:
    eventemitter3: private
  events@3.3.0:
    events: private
  execa@8.0.1:
    execa: private
  extract-zip@2.0.1:
    extract-zip: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-diff@1.3.0:
    fast-diff: private
  fast-fifo@1.3.2:
    fast-fifo: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-uri@3.0.6:
    fast-uri: private
  fast-xml-parser@4.5.3:
    fast-xml-parser: private
  fastq@1.19.1:
    fastq: private
  fd-slicer@1.1.0:
    fd-slicer: private
  fdir@6.4.5(picomatch@4.0.2):
    fdir: private
  fflate@0.8.2:
    fflate: private
  file-entry-cache@6.0.1:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@3.2.0:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  for-each@0.3.5:
    for-each: private
  foreground-child@3.3.1:
    foreground-child: private
  form-data-encoder@1.7.2:
    form-data-encoder: private
  form-data@4.0.2:
    form-data: private
  formdata-node@4.4.1:
    formdata-node: private
  fraction.js@4.3.7:
    fraction.js: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-east-asian-width@1.3.0:
    get-east-asian-width: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@8.0.1:
    get-stream: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  get-uri@6.0.4:
    get-uri: private
  glob-parent@6.0.2:
    glob-parent: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  glob@11.0.2:
    glob: private
  globals@13.24.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  globby@11.1.0:
    globby: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  groq-sdk@0.19.0:
    groq-sdk: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  http-proxy-agent@7.0.2:
    http-proxy-agent: private
  https-proxy-agent@7.0.6:
    https-proxy-agent: private
  human-signals@5.0.0:
    human-signals: private
  humanize-ms@1.2.1:
    humanize-ms: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  internal-slot@1.1.0:
    internal-slot: private
  ip-address@9.0.5:
    ip-address: private
  is-arguments@1.2.0:
    is-arguments: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@4.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-map@2.0.3:
    is-map: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-regex@1.2.1:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-stream@3.0.0:
    is-stream: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  isarray@2.0.5:
    isarray: private
  isexe@2.0.0:
    isexe: private
  iterator.prototype@1.1.5:
    iterator.prototype: private
  jackspeak@4.1.1:
    jackspeak: private
  jest-worker@27.5.1:
    jest-worker: private
  jiti@1.21.7:
    jiti: private
  js-tiktoken@1.0.20:
    js-tiktoken: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsbn@1.1.0:
    jsbn: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@1.0.2:
    json5: private
  jsonrepair@3.12.0:
    jsonrepair: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  keyv@4.5.4:
    keyv: private
  langsmith@0.3.29(openai@4.103.0(ws@8.18.2)(zod@3.25.58)):
    langsmith: private
  language-subtag-registry@0.3.23:
    language-subtag-registry: private
  language-tags@1.0.9:
    language-tags: private
  levn@0.4.1:
    levn: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  listr2@8.3.3:
    listr2: private
  loader-runner@4.3.0:
    loader-runner: private
  loader-utils@3.3.1:
    loader-utils: private
  locate-path@6.0.0:
    locate-path: private
  lodash.merge@4.6.2:
    lodash.merge: private
  log-update@6.1.0:
    log-update: private
  loose-envify@1.4.0:
    loose-envify: private
  lru-cache@11.1.0:
    lru-cache: private
  magic-string@0.30.17:
    magic-string: private
  make-error@1.3.6:
    make-error: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mimic-fn@4.0.0:
    mimic-fn: private
  mimic-function@5.0.1:
    mimic-function: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  mitt@3.0.1:
    mitt: private
  mrmime@1.0.1:
    mrmime: private
  ms@2.1.3:
    ms: private
  mustache@4.2.0:
    mustache: private
  mylas@2.1.13:
    mylas: private
  mz@2.7.0:
    mz: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  neo-async@2.6.2:
    neo-async: private
  netmask@2.0.2:
    netmask: private
  node-domexception@1.0.0:
    node-domexception: private
  node-fetch@2.7.0:
    node-fetch: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  npm-run-path@5.3.0:
    npm-run-path: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  object-inspect@1.13.4:
    object-inspect: private
  object-is@1.1.6:
    object-is: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  object.entries@1.1.9:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.groupby@1.0.3:
    object.groupby: private
  object.values@1.2.1:
    object.values: private
  ollama@0.5.15:
    ollama: private
  once@1.4.0:
    once: private
  onetime@6.0.0:
    onetime: private
  openai@4.103.0(ws@8.18.2)(zod@3.25.58):
    openai: private
  optionator@0.9.4:
    optionator: private
  own-keys@1.0.1:
    own-keys: private
  p-finally@1.0.0:
    p-finally: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-queue@6.6.2:
    p-queue: private
  p-retry@4.6.2:
    p-retry: private
  p-timeout@3.2.0:
    p-timeout: private
  pac-proxy-agent@7.2.0:
    pac-proxy-agent: private
  pac-resolver@7.0.1:
    pac-resolver: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  packages/dev-utils:
    '@extension/dev-utils': private
  packages/hmr:
    '@extension/hmr': private
  packages/i18n:
    '@extension/i18n': private
  packages/schema-utils:
    '@extension/schema-utils': private
  packages/shared:
    '@extension/shared': private
  packages/storage:
    '@extension/storage': private
  packages/tailwind-config:
    '@extension/tailwindcss-config': private
  packages/tsconfig:
    '@extension/tsconfig': private
  packages/ui:
    '@extension/ui': private
  packages/vite-config:
    '@extension/vite-config': private
  packages/zipper:
    '@extension/zipper': private
  pages/content:
    '@extension/content-script': private
  pages/options:
    '@extension/options': private
  pages/side-panel:
    '@extension/sidepanel': private
  parent-module@1.0.1:
    parent-module: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@2.0.0:
    path-scurry: private
  path-type@4.0.0:
    path-type: private
  pend@1.2.0:
    pend: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  pidtree@0.6.0:
    pidtree: private
  pify@2.3.0:
    pify: private
  pirates@4.0.7:
    pirates: private
  plimit-lit@1.6.1:
    plimit-lit: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-import@15.1.0(postcss@8.5.3):
    postcss-import: private
  postcss-js@4.0.1(postcss@8.5.3):
    postcss-js: private
  postcss-load-config@6.0.1(jiti@1.21.7)(postcss@8.5.3)(tsx@4.19.4)(yaml@2.8.0):
    postcss-load-config: private
  postcss-nested@6.2.0(postcss@8.5.3):
    postcss-nested: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prettier-linter-helpers@1.0.0:
    prettier-linter-helpers: private
  progress@2.0.3:
    progress: private
  prop-types@15.8.1:
    prop-types: private
  proxy-agent@6.5.0:
    proxy-agent: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  pump@3.0.2:
    pump: private
  punycode@2.3.1:
    punycode: private
  puppeteer-core@24.10.1:
    puppeteer-core: private
  queue-lit@1.5.2:
    queue-lit: private
  queue-microtask@1.2.3:
    queue-microtask: private
  randombytes@2.1.0:
    randombytes: private
  react-icons@5.5.0(react@18.3.1):
    react-icons: private
  react-is@16.13.1:
    react-is: private
  read-cache@1.0.0:
    read-cache: private
  readdirp@3.6.0:
    readdirp: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  require-directory@2.1.1:
    require-directory: private
  require-from-string@2.0.2:
    require-from-string: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve@2.0.0-next.5:
    resolve: private
  restore-cursor@5.1.0:
    restore-cursor: private
  retry@0.13.1:
    retry: private
  reusify@1.1.0:
    reusify: private
  rfdc@1.4.1:
    rfdc: private
  rollup@4.41.1:
    rollup: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  scheduler@0.23.2:
    scheduler: private
  schema-utils@4.3.2:
    schema-utils: private
  semver@7.7.2:
    semver: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-wcswidth@1.0.1:
    simple-wcswidth: private
  slash@3.0.0:
    slash: private
  slice-ansi@5.0.0:
    slice-ansi: private
  smart-buffer@4.2.0:
    smart-buffer: private
  socks-proxy-agent@8.0.5:
    socks-proxy-agent: private
  socks@2.8.4:
    socks: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.7.4:
    source-map: private
  sprintf-js@1.1.3:
    sprintf-js: private
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: private
  streamx@2.22.0:
    streamx: private
  string-argv@0.3.2:
    string-argv: private
  string-width@4.2.3:
    string-width-cjs: private
  string-width@7.2.0:
    string-width: private
  string.prototype.includes@2.0.1:
    string.prototype.includes: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-final-newline@3.0.0:
    strip-final-newline: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  strnum@1.1.2:
    strnum: private
  sucrase@3.35.0:
    sucrase: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  synckit@0.10.3:
    synckit: private
  tailwind-merge@2.6.0:
    tailwind-merge: private
  tapable@2.2.2:
    tapable: private
  tar-fs@3.0.9:
    tar-fs: private
  tar-stream@3.1.7:
    tar-stream: private
  terser-webpack-plugin@5.3.14(@swc/core@1.11.29)(esbuild@0.25.5)(webpack@5.99.9(@swc/core@1.11.29)(esbuild@0.25.5)):
    terser-webpack-plugin: private
  terser@5.40.0:
    terser: private
  text-decoder@1.2.3:
    text-decoder: private
  text-table@0.2.0:
    text-table: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  tinyglobby@0.2.14:
    tinyglobby: private
  to-regex-range@5.0.1:
    to-regex-range: private
  tr46@0.0.3:
    tr46: private
  ts-api-utils@1.4.3(typescript@5.5.4):
    ts-api-utils: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  ts-loader@9.5.2(typescript@5.5.4)(webpack@5.99.9(@swc/core@1.11.29)(esbuild@0.25.5)):
    ts-loader: private
  ts-node@10.9.2(@swc/core@1.11.29)(@types/node@22.15.23)(typescript@5.5.4):
    ts-node: private
  tsc-alias@1.8.16:
    tsc-alias: private
  tsconfig-paths@3.15.0:
    tsconfig-paths: private
  tsx@4.19.4:
    tsx: private
  turbo-darwin-64@2.5.3:
    turbo-darwin-64: private
  turbo-darwin-arm64@2.5.3:
    turbo-darwin-arm64: private
  turbo-linux-64@2.5.3:
    turbo-linux-64: private
  turbo-linux-arm64@2.5.3:
    turbo-linux-arm64: private
  turbo-windows-64@2.5.3:
    turbo-windows-64: private
  turbo-windows-arm64@2.5.3:
    turbo-windows-arm64: private
  type-check@0.4.0:
    type-check: private
  type-fest@0.20.2:
    type-fest: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  typed-query-selector@2.12.0:
    typed-query-selector: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  undici-types@6.21.0:
    undici-types: private
  update-browserslist-db@1.1.3(browserslist@4.24.5):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  util-deprecate@1.0.2:
    util-deprecate: private
  uuid@10.0.0:
    uuid: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  watchpack@2.4.4:
    watchpack: private
  web-streams-polyfill@4.0.0-beta.3:
    web-streams-polyfill: private
  webextension-polyfill@0.12.0:
    webextension-polyfill: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  webpack-sources@3.3.0:
    webpack-sources: private
  webpack@5.99.9(@swc/core@1.11.29)(esbuild@0.25.5):
    webpack: private
  whatwg-fetch@3.6.20:
    whatwg-fetch: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@9.0.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  ws@8.18.0:
    ws: private
  y18n@5.0.8:
    y18n: private
  yaml@2.8.0:
    yaml: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yauzl@2.10.0:
    yauzl: private
  yn@3.1.1:
    yn: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zod-to-json-schema@3.24.5(zod@3.25.58):
    zod-to-json-schema: private
  zod@3.25.58:
    zod: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.15.1
pendingBuilds: []
prunedAt: Mon, 21 Jul 2025 11:41:52 GMT
publicHoistPattern:
  - '@testing-library/dom'
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.5'
  - '@esbuild/android-arm64@0.25.5'
  - '@esbuild/android-arm@0.25.5'
  - '@esbuild/android-x64@0.25.5'
  - '@esbuild/darwin-x64@0.25.5'
  - '@esbuild/freebsd-arm64@0.25.5'
  - '@esbuild/freebsd-x64@0.25.5'
  - '@esbuild/linux-arm64@0.25.5'
  - '@esbuild/linux-arm@0.25.5'
  - '@esbuild/linux-ia32@0.25.5'
  - '@esbuild/linux-loong64@0.25.5'
  - '@esbuild/linux-mips64el@0.25.5'
  - '@esbuild/linux-ppc64@0.25.5'
  - '@esbuild/linux-riscv64@0.25.5'
  - '@esbuild/linux-s390x@0.25.5'
  - '@esbuild/linux-x64@0.25.5'
  - '@esbuild/netbsd-arm64@0.25.5'
  - '@esbuild/netbsd-x64@0.25.5'
  - '@esbuild/openbsd-arm64@0.25.5'
  - '@esbuild/openbsd-x64@0.25.5'
  - '@esbuild/sunos-x64@0.25.5'
  - '@esbuild/win32-arm64@0.25.5'
  - '@esbuild/win32-ia32@0.25.5'
  - '@esbuild/win32-x64@0.25.5'
  - '@rollup/rollup-android-arm-eabi@4.41.1'
  - '@rollup/rollup-android-arm64@4.41.1'
  - '@rollup/rollup-darwin-x64@4.41.1'
  - '@rollup/rollup-freebsd-arm64@4.41.1'
  - '@rollup/rollup-freebsd-x64@4.41.1'
  - '@rollup/rollup-linux-arm-gnueabihf@4.41.1'
  - '@rollup/rollup-linux-arm-musleabihf@4.41.1'
  - '@rollup/rollup-linux-arm64-gnu@4.41.1'
  - '@rollup/rollup-linux-arm64-musl@4.41.1'
  - '@rollup/rollup-linux-loongarch64-gnu@4.41.1'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.41.1'
  - '@rollup/rollup-linux-riscv64-gnu@4.41.1'
  - '@rollup/rollup-linux-riscv64-musl@4.41.1'
  - '@rollup/rollup-linux-s390x-gnu@4.41.1'
  - '@rollup/rollup-linux-x64-gnu@4.41.1'
  - '@rollup/rollup-linux-x64-musl@4.41.1'
  - '@rollup/rollup-win32-arm64-msvc@4.41.1'
  - '@rollup/rollup-win32-ia32-msvc@4.41.1'
  - '@rollup/rollup-win32-x64-msvc@4.41.1'
  - '@swc/core-darwin-x64@1.11.29'
  - '@swc/core-linux-arm-gnueabihf@1.11.29'
  - '@swc/core-linux-arm64-gnu@1.11.29'
  - '@swc/core-linux-arm64-musl@1.11.29'
  - '@swc/core-linux-x64-gnu@1.11.29'
  - '@swc/core-linux-x64-musl@1.11.29'
  - '@swc/core-win32-arm64-msvc@1.11.29'
  - '@swc/core-win32-ia32-msvc@1.11.29'
  - '@swc/core-win32-x64-msvc@1.11.29'
  - turbo-darwin-64@2.5.3
  - turbo-linux-64@2.5.3
  - turbo-linux-arm64@2.5.3
  - turbo-windows-64@2.5.3
  - turbo-windows-arm64@2.5.3
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
