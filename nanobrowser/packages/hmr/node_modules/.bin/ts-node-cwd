#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1.11.29_@types+node@22.15.23_typescript@5.5.4/node_modules/ts-node/dist/node_modules:/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1.11.29_@types+node@22.15.23_typescript@5.5.4/node_modules/ts-node/node_modules:/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1.11.29_@types+node@22.15.23_typescript@5.5.4/node_modules:/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1.11.29_@types+node@22.15.23_typescript@5.5.4/node_modules/ts-node/dist/node_modules:/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1.11.29_@types+node@22.15.23_typescript@5.5.4/node_modules/ts-node/node_modules:/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1.11.29_@types+node@22.15.23_typescript@5.5.4/node_modules:/Users/<USER>/Desktop/Internship_25/ui-testing-backend/nanobrowser/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../ts-node/dist/bin-cwd.js" "$@"
else
  exec node  "$basedir/../ts-node/dist/bin-cwd.js" "$@"
fi
