import { useState, useEffect, useCallback } from 'react';
import { Button } from '@extension/ui';

interface VectorDBSettingsProps {
  isDarkMode?: boolean;
}

interface DatabaseStats {
  total_vectors: number;
  dimension: number;
  index_fullness: number;
}

interface SearchResult {
  id: string;
  text: string;
  score: number;
  metadata: Record<string, any>;
}

export const VectorDBSettings = ({ isDarkMode = false }: VectorDBSettingsProps) => {
  const [backendUrl, setBackendUrl] = useState('http://localhost:8001');
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<DatabaseStats | null>(null);
  
  // Document management
  const [newDocumentText, setNewDocumentText] = useState('');
  const [documentMetadata, setDocumentMetadata] = useState('{}');
  const [isAddingDocument, setIsAddingDocument] = useState(false);
  
  // Search functionality
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  
  // File upload
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  // Check backend connection
  const checkConnection = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`${backendUrl}/api/v1/health`);
      if (response.ok) {
        setIsConnected(true);
        // Get database stats
        const statsResponse = await fetch(`${backendUrl}/api/v1/stats`);
        if (statsResponse.ok) {
          const statsData = await statsResponse.json();
          setStats(statsData);
        }
      } else {
        setIsConnected(false);
        setError('Backend server not responding');
      }
    } catch (err) {
      setIsConnected(false);
      setError(err instanceof Error ? err.message : 'Connection failed');
    } finally {
      setIsLoading(false);
    }
  }, [backendUrl]);

  // Add document
  const addDocument = useCallback(async () => {
    if (!newDocumentText.trim()) return;
    
    setIsAddingDocument(true);
    setError(null);
    
    try {
      let metadata = {};
      try {
        metadata = JSON.parse(documentMetadata);
      } catch {
        metadata = {};
      }
      
      const response = await fetch(`${backendUrl}/api/v1/documents`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: newDocumentText,
          metadata: metadata,
        }),
      });
      
      if (response.ok) {
        setNewDocumentText('');
        setDocumentMetadata('{}');
        await checkConnection(); // Refresh stats
      } else {
        const errorData = await response.json();
        setError(errorData.detail || 'Failed to add document');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add document');
    } finally {
      setIsAddingDocument(false);
    }
  }, [newDocumentText, documentMetadata, backendUrl, checkConnection]);

  // Search documents
  const searchDocuments = useCallback(async () => {
    if (!searchQuery.trim()) return;
    
    setIsSearching(true);
    setError(null);
    
    try {
      const response = await fetch(`${backendUrl}/api/v1/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: searchQuery,
          top_k: 5,
        }),
      });
      
      if (response.ok) {
        const data = await response.json();
        setSearchResults(data.results || []);
      } else {
        const errorData = await response.json();
        setError(errorData.detail || 'Search failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Search failed');
    } finally {
      setIsSearching(false);
    }
  }, [searchQuery, backendUrl]);

  // Upload file
  const uploadFile = useCallback(async () => {
    if (!selectedFile) return;
    
    setIsUploading(true);
    setError(null);
    
    try {
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('metadata', JSON.stringify({ filename: selectedFile.name }));
      
      const response = await fetch(`${backendUrl}/api/v1/documents/upload`, {
        method: 'POST',
        body: formData,
      });
      
      if (response.ok) {
        setSelectedFile(null);
        await checkConnection(); // Refresh stats
      } else {
        const errorData = await response.json();
        setError(errorData.detail || 'File upload failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'File upload failed');
    } finally {
      setIsUploading(false);
    }
  }, [selectedFile, backendUrl, checkConnection]);

  // Clear database
  const clearDatabase = useCallback(async () => {
    if (!confirm('Are you sure you want to clear all documents from the database? This action cannot be undone.')) {
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`${backendUrl}/api/v1/clear`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        await checkConnection(); // Refresh stats
        setSearchResults([]);
      } else {
        const errorData = await response.json();
        setError(errorData.detail || 'Failed to clear database');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to clear database');
    } finally {
      setIsLoading(false);
    }
  }, [backendUrl, checkConnection]);

  // Check connection on mount and when URL changes
  useEffect(() => {
    checkConnection();
  }, [checkConnection]);

  return (
    <div className="space-y-6 p-6">
      <div>
        <h2 className={`mb-4 text-2xl font-bold ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
          Vector Database Management
        </h2>
        <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          Manage your Pinecone vector database for prompt refinement
        </p>
      </div>

      {/* Backend Connection */}
      <div className={`rounded-lg border p-4 ${isDarkMode ? 'border-slate-700 bg-slate-800' : 'border-gray-200 bg-white'}`}>
        <h3 className={`mb-3 text-lg font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
          Backend Connection
        </h3>
        
        <div className="space-y-3">
          <div>
            <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              Backend URL
            </label>
            <input
              type="url"
              value={backendUrl}
              onChange={(e) => setBackendUrl(e.target.value)}
              className={`mt-1 block w-full rounded-md border px-3 py-2 ${
                isDarkMode
                  ? 'border-slate-600 bg-slate-700 text-gray-200'
                  : 'border-gray-300 bg-white text-gray-900'
              }`}
              placeholder="http://localhost:8001"
            />
          </div>
          
          <div className="flex items-center gap-3">
            <Button
              onClick={checkConnection}
              disabled={isLoading}
              className={`${
                isDarkMode ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-500 hover:bg-blue-600'
              } text-white`}>
              {isLoading ? 'Checking...' : 'Test Connection'}
            </Button>
            
            <div className="flex items-center gap-2">
              <div
                className={`h-3 w-3 rounded-full ${
                  isConnected ? 'bg-green-500' : 'bg-red-500'
                }`}
              />
              <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
          </div>
        </div>
      </div>
