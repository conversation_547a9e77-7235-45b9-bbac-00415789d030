import { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { FaMicrophone } from 'react-icons/fa';
import { AiOutlineLoading3Quarters } from 'react-icons/ai';
import { FiRefreshCw, FiCheck, FiX } from 'react-icons/fi';

interface ChatInputProps {
  onSendMessage: (text: string) => void;
  onStopTask: () => void;
  onMicClick?: () => void;
  isRecording?: boolean;
  isProcessingSpeech?: boolean;
  disabled: boolean;
  showStopButton: boolean;
  setContent?: (setter: (text: string) => void) => void;
  isDarkMode?: boolean;
  // Historical session ID - if provided, shows replay button instead of send button
  historicalSessionId?: string | null;
  onReplay?: (sessionId: string) => void;
  // Auto refinement props
  enableAutoRefinement?: boolean;
  backendUrl?: string;
}

export default function ChatInput({
  onSendMessage,
  onStopTask,
  onMicClick,
  isRecording = false,
  isProcessingSpeech = false,
  disabled,
  showStopButton,
  setContent,
  isDarkMode = false,
  historicalSessionId,
  onReplay,
  enableAutoRefinement = true,
  backendUrl = 'http://localhost:8001',
}: ChatInputProps) {
  const [text, setText] = useState('');
  const [refinedPrompt, setRefinedPrompt] = useState<string | null>(null);
  const [isRefining, setIsRefining] = useState(false);
  const [showRefinement, setShowRefinement] = useState(false);
  const [refinementError, setRefinementError] = useState<string | null>(null);
  const isSendButtonDisabled = useMemo(() => disabled || text.trim() === '', [disabled, text]);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto refinement function
  const refinePrompt = useCallback(async (prompt: string) => {
    if (!enableAutoRefinement || !prompt.trim()) return;

    setIsRefining(true);
    setRefinementError(null);

    try {
      const response = await fetch(`${backendUrl}/api/v1/auto-refine`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ prompt: prompt.trim() }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success && data.final_prompt !== prompt.trim()) {
        setRefinedPrompt(data.final_prompt);
        setShowRefinement(true);
      } else {
        // No refinement needed or failed
        setRefinedPrompt(null);
        setShowRefinement(false);
      }
    } catch (error) {
      console.error('Auto refinement failed:', error);
      setRefinementError(error instanceof Error ? error.message : 'Refinement failed');
      setRefinedPrompt(null);
      setShowRefinement(false);
    } finally {
      setIsRefining(false);
    }
  }, [enableAutoRefinement, backendUrl]);

  // Handle text changes and resize textarea
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = e.target.value;
    setText(newText);

    // Reset refinement state when text changes
    setRefinedPrompt(null);
    setShowRefinement(false);
    setRefinementError(null);

    // Resize textarea
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 100)}px`;
    }
  };

  // Expose a method to set content from outside
  useEffect(() => {
    if (setContent) {
      setContent(setText);
    }
  }, [setContent]);

  // Initial resize when component mounts
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 100)}px`;
    }
  }, []);

  // Handle refinement actions
  const handleRefineClick = useCallback(() => {
    if (text.trim()) {
      refinePrompt(text.trim());
    }
  }, [text, refinePrompt]);

  const handleAcceptRefinement = useCallback(() => {
    if (refinedPrompt) {
      setText(refinedPrompt);
      setShowRefinement(false);
      setRefinedPrompt(null);
    }
  }, [refinedPrompt]);

  const handleRejectRefinement = useCallback(() => {
    setShowRefinement(false);
    setRefinedPrompt(null);
    setRefinementError(null);
  }, []);

  const handleSubmit = useCallback(
    (e: React.FormEvent) => {
      e.preventDefault();
      if (text.trim()) {
        onSendMessage(text);
        setText('');
        setRefinedPrompt(null);
        setShowRefinement(false);
        setRefinementError(null);
      }
    },
    [text, onSendMessage],
  );

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' && !e.shiftKey && !e.nativeEvent.isComposing) {
        e.preventDefault();
        handleSubmit(e);
      }
    },
    [handleSubmit],
  );

  const handleReplay = useCallback(() => {
    if (historicalSessionId && onReplay) {
      onReplay(historicalSessionId);
    }
  }, [historicalSessionId, onReplay]);

  return (
    <div className="space-y-2">
      {/* Refinement suggestion */}
      {showRefinement && refinedPrompt && (
        <div className={`rounded-lg border p-3 ${isDarkMode ? 'border-slate-600 bg-slate-800' : 'border-blue-200 bg-blue-50'}`}>
          <div className="mb-2 flex items-center justify-between">
            <span className={`text-sm font-medium ${isDarkMode ? 'text-blue-400' : 'text-blue-700'}`}>
              Refined Prompt Suggestion
            </span>
            <div className="flex gap-1">
              <button
                type="button"
                onClick={handleAcceptRefinement}
                className={`rounded p-1 transition-colors ${isDarkMode ? 'hover:bg-slate-700 text-green-400' : 'hover:bg-blue-100 text-green-600'}`}
                title="Accept refinement">
                <FiCheck className="h-4 w-4" />
              </button>
              <button
                type="button"
                onClick={handleRejectRefinement}
                className={`rounded p-1 transition-colors ${isDarkMode ? 'hover:bg-slate-700 text-red-400' : 'hover:bg-blue-100 text-red-600'}`}
                title="Reject refinement">
                <FiX className="h-4 w-4" />
              </button>
            </div>
          </div>
          <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            {refinedPrompt}
          </p>
        </div>
      )}

      {/* Refinement error */}
      {refinementError && (
        <div className={`rounded-lg border p-3 ${isDarkMode ? 'border-red-600 bg-red-900/20' : 'border-red-200 bg-red-50'}`}>
          <div className="flex items-center justify-between">
            <span className={`text-sm font-medium ${isDarkMode ? 'text-red-400' : 'text-red-700'}`}>
              Refinement Error
            </span>
            <button
              type="button"
              onClick={() => setRefinementError(null)}
              className={`rounded p-1 transition-colors ${isDarkMode ? 'hover:bg-red-800 text-red-400' : 'hover:bg-red-100 text-red-600'}`}>
              <FiX className="h-4 w-4" />
            </button>
          </div>
          <p className={`text-sm ${isDarkMode ? 'text-red-300' : 'text-red-600'}`}>
            {refinementError}
          </p>
        </div>
      )}

      <form
        onSubmit={handleSubmit}
        className={`overflow-hidden rounded-lg border transition-colors ${disabled ? 'cursor-not-allowed' : 'focus-within:border-sky-400 hover:border-sky-400'} ${isDarkMode ? 'border-slate-700' : ''}`}
        aria-label="Chat input form">
        <div className="flex flex-col">
          <textarea
            ref={textareaRef}
            value={text}
            onChange={handleTextChange}
            onKeyDown={handleKeyDown}
            disabled={disabled}
            aria-disabled={disabled}
            rows={5}
            className={`w-full resize-none border-none p-2 focus:outline-none ${
              disabled
                ? isDarkMode
                  ? 'cursor-not-allowed bg-slate-800 text-gray-400'
                  : 'cursor-not-allowed bg-gray-100 text-gray-500'
                : isDarkMode
                  ? 'bg-slate-800 text-gray-200'
                  : 'bg-white'
            }`}
            placeholder="What can I help you with?"
            aria-label="Message input"
          />

          <div
            className={`flex items-center justify-between px-2 py-1.5 ${
              disabled ? (isDarkMode ? 'bg-slate-800' : 'bg-gray-100') : isDarkMode ? 'bg-slate-800' : 'bg-white'
            }`}>
            <div className="flex gap-2 text-gray-500">
              {onMicClick && (
                <button
                  type="button"
                  onClick={onMicClick}
                  disabled={disabled || isProcessingSpeech}
                  aria-label={
                    isProcessingSpeech ? 'Processing speech...' : isRecording ? 'Stop recording' : 'Start voice input'
                  }
                  className={`rounded-md p-1.5 transition-colors ${
                    disabled || isProcessingSpeech
                      ? 'cursor-not-allowed opacity-50'
                      : isRecording
                        ? 'bg-red-500 text-white hover:bg-red-600'
                        : isDarkMode
                          ? 'hover:bg-slate-700 text-gray-400 hover:text-gray-200'
                          : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
                  }`}>
                  {isProcessingSpeech ? (
                    <AiOutlineLoading3Quarters className="h-4 w-4 animate-spin" />
                  ) : (
                    <FaMicrophone className={`h-4 w-4 ${isRecording ? 'animate-pulse' : ''}`} />
                  )}
                </button>
              )}

              {/* Auto refinement button */}
              {enableAutoRefinement && !historicalSessionId && (
                <button
                  type="button"
                  onClick={handleRefineClick}
                  disabled={disabled || isRefining || !text.trim()}
                  aria-label={isRefining ? 'Refining prompt...' : 'Refine prompt'}
                  className={`rounded-md p-1.5 transition-colors ${
                    disabled || isRefining || !text.trim()
                      ? 'cursor-not-allowed opacity-50'
                      : isDarkMode
                        ? 'hover:bg-slate-700 text-gray-400 hover:text-gray-200'
                        : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
                  }`}>
                  {isRefining ? (
                    <AiOutlineLoading3Quarters className="h-4 w-4 animate-spin" />
                  ) : (
                    <FiRefreshCw className="h-4 w-4" />
                  )}
                </button>
              )}
            </div>

            {showStopButton ? (
              <button
                type="button"
                onClick={onStopTask}
                className="rounded-md bg-red-500 px-3 py-1 text-white transition-colors hover:bg-red-600">
                Stop
              </button>
            ) : historicalSessionId ? (
              <button
                type="button"
                onClick={handleReplay}
                disabled={!historicalSessionId}
                aria-disabled={!historicalSessionId}
                className={`rounded-md bg-green-500 px-3 py-1 text-white transition-colors hover:enabled:bg-green-600 ${!historicalSessionId ? 'cursor-not-allowed opacity-50' : ''}`}>
                Replay
              </button>
            ) : (
              <button
                type="submit"
                disabled={isSendButtonDisabled}
                aria-disabled={isSendButtonDisabled}
                className={`rounded-md bg-[#19C2FF] px-3 py-1 text-white transition-colors hover:enabled:bg-[#0073DC] ${isSendButtonDisabled ? 'cursor-not-allowed opacity-50' : ''}`}>
                Send
              </button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
}
